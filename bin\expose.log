10:48:49: expose 5.2.007
10:48:49: started
10:48:49: expose . converting sections  . 
10:48:49: expose . found Application:General(Test)
10:48:49: Plugin Application:General(Test) (016DED38)
10:48:49: expose . Application:General(Test)  .  converting Section Transformation
10:48:49: expose . Application:General(Test)  .  converting Section Source
10:48:49: expose . Application:General(Test) . found Source:Point
10:48:49: Plugin Source:Point (02FA3C70)
10:48:49: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:48:49: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:48:49: expose . Application:General(Test) . Source:Point . Direction: not used
10:48:49: expose . Application:General(Test) . Source:Point . RayCount: not used
10:48:49: expose . Application:General(Test) . Source:Point .  setup OK
10:48:49: expose . Application:General(Test)  .  converting Section Detector
10:48:49: expose . Application:General(Test) . found Detector:Plane
10:48:49: Plugin Detector:Plane (016DE3B0)
10:48:49: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:48:49: expose . Application:General(Test) . Detector:Plane .  setup OK
10:48:49: expose . Application:General(Test) . looking for engines ...
10:48:49: expose . Application:General(Test) . converting sections  . 
10:48:49: expose . Application:General(Test) . found Segment:Elements
10:48:49: Plugin Segment:Elements (02FA47F0)
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section Transformation
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section Cutter
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section PostProcessing
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section Source
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section Detector
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section BaseSurface
10:48:49: expose . Application:General(Test) . Segment:Elements . found BaseSurface:Plane
10:48:49: Plugin BaseSurface:Plane (02FA4EE0)
10:48:49: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SurfaceTransformation
10:48:49: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SpaceGrid
10:48:49: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane ... converting parameter ReferencePoint
10:48:49: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane .  setup OK
10:48:49: expose . Application:General(Test) . Segment:Elements  .  converting Section Grid
10:48:49: expose . Application:General(Test) . Segment:Elements . found Grid:Rectangular
10:48:49: Plugin Grid:Rectangular (02FA5370)
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular  .  converting Section Transformation
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular  .  converting Section BaseSurface
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter StartPoint
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter UCellCount
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter VCellCount
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular .  setup OK
10:48:49: expose . Application:General(Test) . Segment:Elements . found Element:Pillow4
10:48:49: Plugin ... Cannot load library D:/S1/dlib/Element/Pillow4.dll: The application has failed to start because its side-by-side configuration is incorrect. Please see the application event log or use the command-line sxstrace.exe tool for more detail.
10:48:49: expose . Application:General(Test) . Segment:Elements . Element:Pillow4 is not valid!
10:48:49: expose . Application:General(Test) . Segment:Elements . Size: not used
10:48:49: expose . Application:General(Test) . Segment:Elements . Element:Shape4: not used
10:48:49: expose . Application:General(Test) . Segment:Elements .  setup NOK
10:48:49: expose . Application:General(Test) .  setup NOK
10:48:49: expose . Delete expose
10:48:49: expose . Delete Application:General(Test)(016DED38)
10:48:49: expose . Application:General(Test) . Delete Application:General(Test)
10:48:49: expose . Application:General(Test) . Delete Source:Point(02FA3C70)
10:48:49: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:48:49: expose . Application:General(Test) . Delete Detector:Plane(016DE3B0)
10:48:49: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:48:49: expose . Application:General(Test) . Delete Segment:Elements(02FA47F0)
10:48:49: expose . Application:General(Test) . Segment:Elements . Delete Segment:Elements
10:48:49: expose . Application:General(Test) . Segment:Elements . Delete BaseSurface:Plane(02FA4EE0)
10:48:49: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane . Delete BaseSurface:Plane
10:48:49: expose . Application:General(Test) . Segment:Elements . Delete Grid:Rectangular(02FA5370)
10:48:49: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular . Delete Grid:Rectangular
