10:56:43: expose 5.2.007
10:56:43: started
10:56:43: expose . converting sections  . 
10:56:43: expose . found Application:General(Test)
10:56:43: Plugin Application:General(Test) (019DED38)
10:56:43: expose . Application:General(Test)  .  converting Section Transformation
10:56:43: expose . Application:General(Test)  .  converting Section Source
10:56:43: expose . Application:General(Test) . found Source:Point
10:56:43: Plugin Source:Point (03183C70)
10:56:43: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:56:43: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:56:43: expose . Application:General(Test) . Source:Point . Direction: not used
10:56:43: expose . Application:General(Test) . Source:Point . RayCount: not used
10:56:43: expose . Application:General(Test) . Source:Point .  setup OK
10:56:43: expose . Application:General(Test)  .  converting Section Detector
10:56:43: expose . Application:General(Test) . found Detector:Plane
10:56:44: Plugin Detector:Plane (019DE328)
10:56:44: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:56:44: expose . Application:General(Test) . Detector:Plane .  setup OK
10:56:44: expose . Application:General(Test) . looking for engines ...
10:56:44: expose . Application:General(Test) . converting sections  . 
10:56:44: expose . Application:General(Test) . found Segment:Paraboloid
10:56:44: Plugin Segment:Paraboloid (03183FE8)
10:56:44: expose . Application:General(Test) . Segment:Paraboloid  .  converting Section Transformation
10:56:44: expose . Application:General(Test) . Segment:Paraboloid  .  converting Section Cutter
10:56:44: expose . Application:General(Test) . Segment:Paraboloid  .  converting Section PostProcessing
10:56:44: expose . Application:General(Test) . Segment:Paraboloid ... converting parameter FocalDistance
10:56:44: expose . Application:General(Test) . Segment:Paraboloid ... converting parameter FocalPoint
10:56:44: expose . Application:General(Test) . Segment:Paraboloid .  setup OK
10:56:44: expose . Application:General(Test) .  setup OK
10:56:44: expose . Application:General(Test) . running ... Segment:Paraboloid
10:56:44: expose . Application:General(Test) . execution of Segment:Paraboloid OK.
10:56:44: expose . Application:General(Test)  .  OK
10:56:44: expose 5.2 finished
10:56:44: expose . Delete expose
10:56:44: expose . Delete Application:General(Test)(019DED38)
10:56:44: expose . Application:General(Test) . Delete Application:General(Test)
10:56:44: expose . Application:General(Test) . Delete Source:Point(03183C70)
10:56:44: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:56:44: expose . Application:General(Test) . Delete Detector:Plane(019DE328)
10:56:44: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:56:44: expose . Application:General(Test) . Delete Segment:Paraboloid(03183FE8)
10:56:44: expose . Application:General(Test) . Segment:Paraboloid . Delete Segment:Paraboloid
