10:36:02: expose 5.2.007
10:36:02: started
10:36:02: expose . converting sections  . 
10:36:02: expose . found Application:General(Test)
10:36:02: Plugin Application:General(Test) (00C4ED38)
10:36:02: expose . Application:General(Test)  .  converting Section Transformation
10:36:02: expose . Application:General(Test)  .  converting Section Source
10:36:02: expose . Application:General(Test) . found Source:Point
10:36:02: Plugin Source:Point (02503C70)
10:36:02: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:36:02: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:36:02: expose . Application:General(Test) . Source:Point . Direction: not used
10:36:02: expose . Application:General(Test) . Source:Point . RayCount: not used
10:36:02: expose . Application:General(Test) . Source:Point .  setup OK
10:36:02: expose . Application:General(Test)  .  converting Section Detector
10:36:02: expose . Application:General(Test) . found Detector:Plane
10:36:02: Plugin Detector:Plane (00C4E328)
10:36:02: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:36:02: expose . Application:General(Test) . Detector:Plane .  setup OK
10:36:02: expose . Application:General(Test) . looking for engines ...
10:36:02: expose . Application:General(Test) . converting sections  . 
10:36:02: expose . Application:General(Test) . found Segment:Test
10:36:02: Plugin Segment:Test (025047F0)
10:36:02: expose . Application:General(Test) . Segment:Test  .  converting Section Transformation
10:36:02: expose . Application:General(Test) . Segment:Test  .  converting Section Cutter
10:36:02: expose . Application:General(Test) . Segment:Test  .  converting Section PostProcessing
10:36:02: expose . Application:General(Test) . Segment:Test ... converting parameter Size
10:36:02: expose . Application:General(Test) . Segment:Test . BaseSurface:Plane: not used
10:36:02: expose . Application:General(Test) . Segment:Test . Grid:Rectangular: not used
10:36:02: expose . Application:General(Test) . Segment:Test .  setup OK
10:36:02: expose . Application:General(Test) .  setup OK
10:36:03: expose . Application:General(Test) . running ... Segment:Test
10:36:03: expose . Application:General(Test) . execution of Segment:Test OK.
10:36:03: expose . Application:General(Test) . exporting to Test.igs
10:36:03: expose . Application:General(Test)  .  OK
10:36:03: expose 5.2 finished
10:36:03: expose . Delete expose
10:36:03: expose . Delete Application:General(Test)(00C4ED38)
10:36:03: expose . Application:General(Test) . Delete Application:General(Test)
10:36:03: expose . Application:General(Test) . Delete Source:Point(02503C70)
10:36:03: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:36:03: expose . Application:General(Test) . Delete Detector:Plane(00C4E328)
10:36:03: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:36:03: expose . Application:General(Test) . Delete Segment:Test(025047F0)
10:36:03: expose . Application:General(Test) . Segment:Test . Delete Segment:Test
