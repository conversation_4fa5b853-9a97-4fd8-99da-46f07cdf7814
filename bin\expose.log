10:44:12: expose 5.2.007
10:44:12: started
10:44:12: expose . converting sections  . 
10:44:12: expose . found Application:General(Test)
10:44:12: Plugin Application:General(Test) (00BBED38)
10:44:12: expose . Application:General(Test)  .  converting Section Transformation
10:44:12: expose . Application:General(Test)  .  converting Section Source
10:44:12: expose . Application:General(Test) . found Source:Point
10:44:12: Plugin Source:Point (026F3C70)
10:44:12: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:44:12: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:44:12: expose . Application:General(Test) . Source:Point . Direction: not used
10:44:12: expose . Application:General(Test) . Source:Point . RayCount: not used
10:44:12: expose . Application:General(Test) . Source:Point .  setup OK
10:44:12: expose . Application:General(Test)  .  converting Section Detector
10:44:12: expose . Application:General(Test) . found Detector:Plane
10:44:12: Plugin Detector:Plane (00BBE3B0)
10:44:12: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:44:12: expose . Application:General(Test) . Detector:Plane .  setup OK
10:44:12: expose . Application:General(Test) . looking for engines ...
10:44:12: expose . Application:General(Test) . converting sections  . 
10:44:12: expose . Application:General(Test) . found Segment:Elements
10:44:12: Plugin Segment:Elements (026F47F0)
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section Transformation
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section Cutter
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section PostProcessing
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section Source
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section Detector
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section BaseSurface
10:44:12: expose . Application:General(Test) . Segment:Elements . found BaseSurface:Plane
10:44:12: Plugin BaseSurface:Plane (026F4EE0)
10:44:12: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SurfaceTransformation
10:44:12: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SpaceGrid
10:44:12: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane ... converting parameter ReferencePoint
10:44:12: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane .  setup OK
10:44:12: expose . Application:General(Test) . Segment:Elements  .  converting Section Grid
10:44:12: expose . Application:General(Test) . Segment:Elements . found Grid:Rectangular
10:44:12: Plugin Grid:Rectangular (026F5370)
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular  .  converting Section Transformation
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular  .  converting Section BaseSurface
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter StartPoint
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter UCellCount
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter VCellCount
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular .  setup OK
10:44:12: expose . Application:General(Test) . Segment:Elements . found Element:Pillow4
10:44:12: Plugin ... Cannot load library D:/S1/dlib/Element/Pillow4.dll: The application has failed to start because its side-by-side configuration is incorrect. Please see the application event log or use the command-line sxstrace.exe tool for more detail.
10:44:12: expose . Application:General(Test) . Segment:Elements . Element:Pillow4 is not valid!
10:44:12: expose . Application:General(Test) . Segment:Elements . Size: not used
10:44:12: expose . Application:General(Test) . Segment:Elements . Element:Pillow4: not used
10:44:12: expose . Application:General(Test) . Segment:Elements .  setup NOK
10:44:12: expose . Application:General(Test) .  setup NOK
10:44:12: expose . Delete expose
10:44:12: expose . Delete Application:General(Test)(00BBED38)
10:44:12: expose . Application:General(Test) . Delete Application:General(Test)
10:44:12: expose . Application:General(Test) . Delete Source:Point(026F3C70)
10:44:12: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:44:12: expose . Application:General(Test) . Delete Detector:Plane(00BBE3B0)
10:44:12: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:44:12: expose . Application:General(Test) . Delete Segment:Elements(026F47F0)
10:44:12: expose . Application:General(Test) . Segment:Elements . Delete Segment:Elements
10:44:12: expose . Application:General(Test) . Segment:Elements . Delete BaseSurface:Plane(026F4EE0)
10:44:12: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane . Delete BaseSurface:Plane
10:44:12: expose . Application:General(Test) . Segment:Elements . Delete Grid:Rectangular(026F5370)
10:44:12: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular . Delete Grid:Rectangular
