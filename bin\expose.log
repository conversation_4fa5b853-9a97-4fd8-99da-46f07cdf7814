10:49:21: expose 5.2.007
10:49:21: started
10:49:21: expose . converting sections  . 
10:49:21: expose . found Application:General(Test)
10:49:21: Plugin Application:General(Test) (012BED38)
10:49:21: expose . Application:General(Test)  .  converting Section Transformation
10:49:21: expose . Application:General(Test)  .  converting Section Source
10:49:21: expose . Application:General(Test) . found Source:Point
10:49:21: Plugin Source:Point (02B73C70)
10:49:21: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:49:21: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:49:21: expose . Application:General(Test) . Source:Point . Direction: not used
10:49:21: expose . Application:General(Test) . Source:Point . RayCount: not used
10:49:21: expose . Application:General(Test) . Source:Point .  setup OK
10:49:21: expose . Application:General(Test)  .  converting Section Detector
10:49:21: expose . Application:General(Test) . found Detector:Plane
10:49:21: Plugin Detector:Plane (012BE3B0)
10:49:21: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:49:21: expose . Application:General(Test) . Detector:Plane .  setup OK
10:49:21: expose . Application:General(Test) . looking for engines ...
10:49:21: expose . Application:General(Test) . converting sections  . 
10:49:21: expose . Application:General(Test) . found Segment:Elements
10:49:21: Plugin Segment:Elements (02B747F0)
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section Transformation
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section Cutter
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section PostProcessing
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section Source
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section Detector
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section BaseSurface
10:49:21: expose . Application:General(Test) . Segment:Elements . found BaseSurface:Plane
10:49:21: Plugin BaseSurface:Plane (02B74EE0)
10:49:21: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SurfaceTransformation
10:49:21: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SpaceGrid
10:49:21: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane ... converting parameter ReferencePoint
10:49:21: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane .  setup OK
10:49:21: expose . Application:General(Test) . Segment:Elements  .  converting Section Grid
10:49:21: expose . Application:General(Test) . Segment:Elements . found Grid:Triangular
10:49:21: Plugin Grid:Triangular (02B75370)
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular  .  converting Section Transformation
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular ... converting parameter StartPoint
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular ... converting parameter PrimaryDirection
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular . PrimaryDirection not defined.
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular ... converting parameter PrimaryLength
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular . PrimaryLength not defined.
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular ... converting parameter UCellCount
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular ... converting parameter VCellCount
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular . Uextent: not used
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular . Vextent: not used
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular .  setup NOK
10:49:21: expose . Application:General(Test) . Segment:Elements . Size: not used
10:49:21: expose . Application:General(Test) . Segment:Elements . Element:Shape3: not used
10:49:21: expose . Application:General(Test) . Segment:Elements .  setup NOK
10:49:21: expose . Application:General(Test) .  setup NOK
10:49:21: expose . Delete expose
10:49:21: expose . Delete Application:General(Test)(012BED38)
10:49:21: expose . Application:General(Test) . Delete Application:General(Test)
10:49:21: expose . Application:General(Test) . Delete Source:Point(02B73C70)
10:49:21: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:49:21: expose . Application:General(Test) . Delete Detector:Plane(012BE3B0)
10:49:21: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:49:21: expose . Application:General(Test) . Delete Segment:Elements(02B747F0)
10:49:21: expose . Application:General(Test) . Segment:Elements . Delete Segment:Elements
10:49:21: expose . Application:General(Test) . Segment:Elements . Delete BaseSurface:Plane(02B74EE0)
10:49:21: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane . Delete BaseSurface:Plane
10:49:21: expose . Application:General(Test) . Segment:Elements . Delete Grid:Triangular(02B75370)
10:49:21: expose . Application:General(Test) . Segment:Elements . Grid:Triangular . Delete Grid:Triangular
