10:55:02: expose 5.2.007
10:55:02: started
10:55:02: expose . converting sections  . 
10:55:02: expose . found Application:General(Test)
10:55:02: Plugin Application:General(Test) (00DCED38)
10:55:02: expose . Application:General(Test)  .  converting Section Transformation
10:55:02: expose . Application:General(Test)  .  converting Section Source
10:55:02: expose . Application:General(Test) . found Source:Point
10:55:02: Plugin Source:Point (02BA3C70)
10:55:02: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:55:02: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:55:02: expose . Application:General(Test) . Source:Point . Direction: not used
10:55:02: expose . Application:General(Test) . Source:Point . RayCount: not used
10:55:02: expose . Application:General(Test) . Source:Point .  setup OK
10:55:02: expose . Application:General(Test)  .  converting Section Detector
10:55:02: expose . Application:General(Test) . found Detector:Plane
10:55:02: Plugin Detector:Plane (00DCE328)
10:55:02: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:55:02: expose . Application:General(Test) . Detector:Plane .  setup OK
10:55:02: expose . Application:General(Test) . looking for engines ...
10:55:02: expose . Application:General(Test) . converting sections  . 
10:55:02: expose . Application:General(Test) . found Segment:Paraboloid
10:55:02: Plugin Segment:Paraboloid (02BA3FE8)
10:55:02: expose . Application:General(Test) . Segment:Paraboloid  .  converting Section Transformation
10:55:02: expose . Application:General(Test) . Segment:Paraboloid  .  converting Section Cutter
10:55:02: expose . Application:General(Test) . Segment:Paraboloid  .  converting Section PostProcessing
10:55:02: expose . Application:General(Test) . Segment:Paraboloid ... converting parameter FocalDistance
10:55:02: expose . Application:General(Test) . Segment:Paraboloid ... converting parameter FocalPoint
10:55:02: expose . Application:General(Test) . Segment:Paraboloid .  setup OK
10:55:02: expose . Application:General(Test) .  setup OK
10:55:02: expose . Application:General(Test) . running ... Segment:Paraboloid
10:55:02: expose . Application:General(Test) . execution of Segment:Paraboloid OK.
10:55:02: expose . Application:General(Test) . exporting to Paraboloid.igs
10:55:32: expose . Application:General(Test)  .  OK
10:55:32: expose 5.2 finished
10:55:32: expose . Delete expose
10:55:32: expose . Delete Application:General(Test)(00DCED38)
10:55:32: expose . Application:General(Test) . Delete Application:General(Test)
10:55:32: expose . Application:General(Test) . Delete Source:Point(02BA3C70)
10:55:32: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:55:32: expose . Application:General(Test) . Delete Detector:Plane(00DCE328)
10:55:32: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:55:32: expose . Application:General(Test) . Delete Segment:Paraboloid(02BA3FE8)
10:55:32: expose . Application:General(Test) . Segment:Paraboloid . Delete Segment:Paraboloid
