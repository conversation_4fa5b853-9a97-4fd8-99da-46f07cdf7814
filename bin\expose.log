10:41:13: expose 5.2.007
10:41:13: started
10:41:13: expose . converting sections  . 
10:41:13: expose . found Application:General(Test)
10:41:13: Plugin Application:General(Test) (0114ED38)
10:41:13: expose . Application:General(Test)  .  converting Section Transformation
10:41:13: expose . Application:General(Test)  .  converting Section Source
10:41:13: expose . Application:General(Test) . found Source:Point
10:41:13: Plugin Source:Point (028F3C70)
10:41:13: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:41:13: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:41:13: expose . Application:General(Test) . Source:Point . Direction: not used
10:41:13: expose . Application:General(Test) . Source:Point . RayCount: not used
10:41:13: expose . Application:General(Test) . Source:Point .  setup OK
10:41:13: expose . Application:General(Test)  .  converting Section Detector
10:41:13: expose . Application:General(Test) . found Detector:Plane
10:41:13: Plugin Detector:Plane (0114E328)
10:41:13: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:41:13: expose . Application:General(Test) . Detector:Plane .  setup OK
10:41:13: expose . Application:General(Test) . looking for engines ...
10:41:13: expose . Application:General(Test) . converting sections  . 
10:41:13: expose . Application:General(Test) . found Segment:Elements
10:41:13: Plugin Segment:Elements (028F47F0)
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section Transformation
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section Cutter
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section PostProcessing
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section Source
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section Detector
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section BaseSurface
10:41:13: expose . Application:General(Test) . Segment:Elements . found BaseSurface:Plane
10:41:13: Plugin BaseSurface:Plane (028F4EE0)
10:41:13: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SurfaceTransformation
10:41:13: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane  .  converting Section SpaceGrid
10:41:13: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane ... converting parameter ReferencePoint
10:41:13: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane .  setup OK
10:41:13: expose . Application:General(Test) . Segment:Elements  .  converting Section Grid
10:41:13: expose . Application:General(Test) . Segment:Elements . found Grid:Rectangular
10:41:13: Plugin Grid:Rectangular (028F5370)
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular  .  converting Section Transformation
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular  .  converting Section BaseSurface
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter StartPoint
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular . StartPoint not defined.
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter UCellCount
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular ... converting parameter VCellCount
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular .  setup NOK
10:41:13: expose . Application:General(Test) . Segment:Elements . Size: not used
10:41:13: expose . Application:General(Test) . Segment:Elements .  setup NOK
10:41:13: expose . Application:General(Test) .  setup NOK
10:41:13: expose . Delete expose
10:41:13: expose . Delete Application:General(Test)(0114ED38)
10:41:13: expose . Application:General(Test) . Delete Application:General(Test)
10:41:13: expose . Application:General(Test) . Delete Source:Point(028F3C70)
10:41:13: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:41:13: expose . Application:General(Test) . Delete Detector:Plane(0114E328)
10:41:13: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:41:13: expose . Application:General(Test) . Delete Segment:Elements(028F47F0)
10:41:13: expose . Application:General(Test) . Segment:Elements . Delete Segment:Elements
10:41:13: expose . Application:General(Test) . Segment:Elements . Delete BaseSurface:Plane(028F4EE0)
10:41:13: expose . Application:General(Test) . Segment:Elements . BaseSurface:Plane . Delete BaseSurface:Plane
10:41:13: expose . Application:General(Test) . Segment:Elements . Delete Grid:Rectangular(028F5370)
10:41:13: expose . Application:General(Test) . Segment:Elements . Grid:Rectangular . Delete Grid:Rectangular
