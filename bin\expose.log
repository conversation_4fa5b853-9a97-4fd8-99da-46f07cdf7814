10:54:14: expose 5.2.007
10:54:14: started
10:54:14: expose . converting sections  . 
10:54:14: expose . found Application:General(Test)
10:54:14: Plugin Application:General(Test) (00DDED38)
10:54:14: expose . Application:General(Test)  .  converting Section Transformation
10:54:14: expose . Application:General(Test)  .  converting Section Source
10:54:14: expose . Application:General(Test) . found Source:Point
10:54:14: Plugin Source:Point (02583C70)
10:54:14: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:54:14: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:54:14: expose . Application:General(Test) . Source:Point . Direction: not used
10:54:14: expose . Application:General(Test) . Source:Point . RayCount: not used
10:54:14: expose . Application:General(Test) . Source:Point .  setup OK
10:54:14: expose . Application:General(Test)  .  converting Section Detector
10:54:14: expose . Application:General(Test) . found Detector:Plane
10:54:14: Plugin Detector:Plane (00DDE328)
10:54:14: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:54:14: expose . Application:General(Test) . Detector:Plane .  setup OK
10:54:14: expose . Application:General(Test) . looking for engines ...
10:54:14: expose . Application:General(Test) . converting sections  . 
10:54:14: expose . Application:General(Test) . found Segment:FreeForm
10:54:15: Plugin Segment:FreeForm (02583FE8)
10:54:15: expose . Application:General(Test) . Segment:FreeForm  .  converting Section Transformation
10:54:15: expose . Application:General(Test) . Segment:FreeForm  .  converting Section Cutter
10:54:15: expose . Application:General(Test) . Segment:FreeForm  .  converting Section PostProcessing
10:54:15: expose . Application:General(Test) . Segment:FreeForm  .  converting Section Source
10:54:15: expose . Application:General(Test) . Segment:FreeForm  .  converting Section Detector
10:54:15: expose . Application:General(Test) . Segment:FreeForm ... converting parameter StartPoint
10:54:15: expose . Application:General(Test) . Segment:FreeForm . StartPoint not defined.
10:54:15: expose . Application:General(Test) . Segment:FreeForm ... converting parameter HorizontalSize
10:54:15: expose . Application:General(Test) . Segment:FreeForm . HorizontalSize not defined.
10:54:15: expose . Application:General(Test) . Segment:FreeForm . Invalid HorizontalSize input.
10:54:15: expose . Application:General(Test) . Segment:FreeForm ... converting parameter VerticalSize
10:54:15: expose . Application:General(Test) . Segment:FreeForm . VerticalSize not defined.
10:54:15: expose . Application:General(Test) . Segment:FreeForm . Invalid VerticalSize input.
10:54:15: expose . Application:General(Test) . Segment:FreeForm  .  converting Section Distribution
10:54:15: expose . Application:General(Test) . Segment:FreeForm . Distribution not defined.
10:54:15: expose . Application:General(Test) . Segment:FreeForm . patch_step 0
10:54:15: expose . Application:General(Test) . Segment:FreeForm . Size: not used
10:54:15: expose . Application:General(Test) . Segment:FreeForm .  setup NOK
10:54:15: expose . Application:General(Test) .  setup NOK
10:54:15: expose . Delete expose
10:54:15: expose . Delete Application:General(Test)(00DDED38)
10:54:15: expose . Application:General(Test) . Delete Application:General(Test)
10:54:15: expose . Application:General(Test) . Delete Source:Point(02583C70)
10:54:15: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:54:15: expose . Application:General(Test) . Delete Detector:Plane(00DDE328)
10:54:15: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:54:15: expose . Application:General(Test) . Delete Segment:FreeForm(02583FE8)
10:54:15: expose . Application:General(Test) . Segment:FreeForm . Delete Segment:FreeForm
