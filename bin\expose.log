10:50:14: expose 5.2.007
10:50:14: started
10:50:14: expose . converting sections  . 
10:50:14: expose . found Application:General(Test)
10:50:14: Plugin Application:General(Test) (0156ED38)
10:50:14: expose . Application:General(Test)  .  converting Section Transformation
10:50:14: expose . Application:General(Test)  .  converting Section Source
10:50:14: expose . Application:General(Test) . found Source:Point
10:50:14: Plugin Source:Point (02D73C70)
10:50:14: expose . Application:General(Test) . Source:Point  .  converting Section SourceTransformation
10:50:14: expose . Application:General(Test) . Source:Point ... converting parameter Position
10:50:14: expose . Application:General(Test) . Source:Point . Direction: not used
10:50:14: expose . Application:General(Test) . Source:Point . RayCount: not used
10:50:14: expose . Application:General(Test) . Source:Point .  setup OK
10:50:14: expose . Application:General(Test)  .  converting Section Detector
10:50:14: expose . Application:General(Test) . found Detector:Plane
10:50:14: Plugin Detector:Plane (0156E328)
10:50:14: expose . Application:General(Test) . Detector:Plane ... converting parameter ReferencePoint
10:50:14: expose . Application:General(Test) . Detector:Plane .  setup OK
10:50:14: expose . Application:General(Test) . looking for engines ...
10:50:14: expose . Application:General(Test) . converting sections  . 
10:50:14: expose . Application:General(Test) . found Segment:Plain
10:50:14: Plugin Segment:Plain (02D73FE8)
10:50:14: expose . Application:General(Test) . Segment:Plain  .  converting Section Transformation
10:50:14: expose . Application:General(Test) . Segment:Plain  .  converting Section Cutter
10:50:14: expose . Application:General(Test) . Segment:Plain  .  converting Section PostProcessing
10:50:14: expose . Application:General(Test) . Segment:Plain . StartPoint: not used
10:50:14: expose . Application:General(Test) . Segment:Plain . HorizontalSize: not used
10:50:14: expose . Application:General(Test) . Segment:Plain . VerticalSize: not used
10:50:14: expose . Application:General(Test) . Segment:Plain . Spread: not used
10:50:14: expose . Application:General(Test) . Segment:Plain .  setup OK
10:50:14: expose . Application:General(Test) .  setup OK
10:50:14: expose . Application:General(Test) . running ... Segment:Plain
10:50:14: expose . Application:General(Test) . execution of Segment:Plain OK.
10:50:14: expose . Application:General(Test) . exporting to Plain.igs
10:50:44: expose . Application:General(Test)  .  OK
10:50:44: expose 5.2 finished
10:50:44: expose . Delete expose
10:50:44: expose . Delete Application:General(Test)(0156ED38)
10:50:44: expose . Application:General(Test) . Delete Application:General(Test)
10:50:44: expose . Application:General(Test) . Delete Source:Point(02D73C70)
10:50:44: expose . Application:General(Test) . Source:Point . Delete Source:Point
10:50:44: expose . Application:General(Test) . Delete Detector:Plane(0156E328)
10:50:44: expose . Application:General(Test) . Detector:Plane . Delete Detector:Plane
10:50:44: expose . Application:General(Test) . Delete Segment:Plain(02D73FE8)
10:50:44: expose . Application:General(Test) . Segment:Plain . Delete Segment:Plain
