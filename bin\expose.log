10:38:48: expose 5.2.007
10:38:48: started
10:38:48: expose . converting sections  . 
10:38:48: expose . found Application:General
10:38:48: Plugin Application:General (00E2ED38)
10:38:48: expose . Application:General  .  converting Section Transformation
10:38:48: expose . Application:General  .  converting Section Source
10:38:48: expose . Application:General . found Source:Point
10:38:48: Plugin Source:Point (025D3C70)
10:38:48: expose . Application:General . Source:Point  .  converting Section SourceTransformation
10:38:48: expose . Application:General . Source:Point ... converting parameter Position
10:38:48: expose . Application:General . Source:Point . Direction: not used
10:38:48: expose . Application:General . Source:Point . RayCount: not used
10:38:48: expose . Application:General . Source:Point .  setup OK
10:38:48: expose . Application:General  .  converting Section Detector
10:38:48: expose . Application:General . found Detector:Plane
10:38:48: Plugin Detector:Plane (00E2E3B0)
10:38:48: expose . Application:General . Detector:Plane ... converting parameter ReferencePoint
10:38:48: expose . Application:General . Detector:Plane .  setup OK
10:38:48: expose . Application:General . looking for engines ...
10:38:48: expose . Application:General . converting sections  . 
10:38:48: expose . Application:General . found Segment:Test
10:38:48: Plugin Segment:Test (025D47F0)
10:38:48: expose . Application:General . Segment:Test  .  converting Section Transformation
10:38:48: expose . Application:General . Segment:Test  .  converting Section Cutter
10:38:48: expose . Application:General . Segment:Test  .  converting Section PostProcessing
10:38:48: expose . Application:General . Segment:Test ... converting parameter Size
10:38:48: expose . Application:General . Segment:Test . BaseSurface:Ellipsoid: not used
10:38:48: expose . Application:General . Segment:Test . Grid:Hexagonal: not used
10:38:48: expose . Application:General . Segment:Test .  setup OK
10:38:48: expose . Application:General .  setup OK
10:38:48: expose . Application:General . running ... Segment:Test
10:38:48: expose . Application:General . execution of Segment:Test OK.
10:38:48: expose . Application:General . exporting to Test.igs
10:38:48: expose . Application:General  .  OK
10:38:48: expose 5.2 finished
10:38:48: expose . Delete expose
10:38:48: expose . Delete Application:General(00E2ED38)
10:38:48: expose . Application:General . Delete Application:General
10:38:48: expose . Application:General . Delete Source:Point(025D3C70)
10:38:48: expose . Application:General . Source:Point . Delete Source:Point
10:38:48: expose . Application:General . Delete Detector:Plane(00E2E3B0)
10:38:48: expose . Application:General . Detector:Plane . Delete Detector:Plane
10:38:48: expose . Application:General . Delete Segment:Test(025D47F0)
10:38:48: expose . Application:General . Segment:Test . Delete Segment:Test
