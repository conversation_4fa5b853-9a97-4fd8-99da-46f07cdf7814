#include "General.hpp"
#include<cstdio>

using namespace expose;
//=========================================================================
GeneralApplication::GeneralApplication()
{
}
//=========================================================================
GeneralApplication::~GeneralApplication()
{ 
}
//=========================================================================
int GeneralApplication::Setup()
{ int ok=Application::Setup();

 printf("DEBUG: GeneralApplication::Setup() started, Application::Setup() returned: %s\n", ok ? "SUCCESS" : "FAILED");
 fflush(stdout);

 ConvertSection("Source"  , (Setable**)&m_source,SILENT);
 printf("DEBUG: ConvertSection Source result: %s\n", m_source ? "SUCCESS" : "FAILED");
 fflush(stdout);

 ConvertSection("Detector", (Setable**)&m_detector,SILENT);
 printf("DEBUG: ConvertSection Detector result: %s\n", m_detector ? "SUCCESS" : "FAILED");
 fflush(stdout);
 
 ConvertParameter("Info",info,SILENT);
    
 QStringList engine_list;
 
 engine_list.push_back("Segment");
 engine_list.push_back("Lens");
 engine_list.push_back("Pipe");
 engine_list.push_back("Optical");
 
 QList<Setable*> engines;
 
 AddInfo("looking for engines ...",Info::RUN);
 printf("DEBUG: Looking for engines: Segment, Lens, Pipe, Optical\n");
 fflush(stdout);

 ok=ConvertSections(engine_list,engines);
 printf("DEBUG: ConvertSections result: %s, found %d engines\n", ok ? "SUCCESS" : "FAILED", engines.size());
 fflush(stdout);
 
 QList<Setable*>::iterator ie;
 for(ie=engines.begin(); ie!=engines.end(); ie++)
 { Engine* eng=(Engine*)*ie ;
  
  m_engines.push_back(eng);
 }
 
 printf("DEBUG: GeneralApplication::Setup() ending with ok=%s\n", ok ? "SUCCESS" : "FAILED");
 fflush(stdout);
 return(ok);
}
//==========================================================================
int GeneralApplication::Run()
{ int is;
  int ok=1, eok;
  QString errmsg(" fatal exception in execution of");
  QString msg;
  QString name;
  
  for(is=0; is<m_engines.size(); is++)
  { 
   try 
   {
    name=m_engines[is]->GetName();
    AddInfo("running ... "+name ,Info::RUN);
    eok=m_engines[is]->RunWithoutException();
    
    AddInfo(m_engines[is]->GetInfo());
    
    msg= eok ? " OK." : " not OK.";
    AddInfo("execution of "+name+ msg,Info::RUN);
    ok*=eok;
   }
   catch(const char* info)
   { 
    AddInfo(name+" ... "+info,Info::ERROR);
    ok=0;
   }
   catch(...)
   { 
    AddInfo(name+" ... "+errmsg,Info::ERROR);
    ok=0;
   }   
  
   ID id(m_engines[is]->GetName());
   Geometry geometry = m_engines[is]->GetResult();
   QString igsfilename=id.GetLabel();

   Geometry *ptgeometry;
   
   Geometry tgeometry;
   
   if(m_transform.IsOne()) ptgeometry=&geometry;
   else
   {
    tgeometry = m_transform.Transform(geometry);
    ptgeometry = &tgeometry;
   }
   
   if(m_exporttype&ExportType_IGS)
   {
    msg="exporting to "+igsfilename+".igs";
    AddInfo(msg,Info::RUN);
    ptgeometry->Export(igsfilename,Geometry::IGS,m_engines[is]->GetInfoTag());    
   }
   if(m_exporttype&ExportType_BGF)
   {
    msg="exporting to "+igsfilename+".bgf";
    AddInfo(msg,Info::RUN);
    ptgeometry->Export(igsfilename,Geometry::BGF,m_engines[is]->GetInfoTag());    
   }
   
  }
//  AppIGS.StartSection((char*)info.data());
//  AppIGS.Author(getenv("LOGNAME"));
//  AppIGS.Write((char*)igsfilename.data());

 return(ok);
}
//==========================================================================
EXPOSE_EXPORT_PLUGIN2(General, GeneralApplication)
